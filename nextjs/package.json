{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "google-auth-library": "^10.1.0", "lucide-react": "^0.525.0", "next": "^15.4.3", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "24.3.0", "@types/react": "19.1.10", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.3", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.4.0", "typescript": "5.9.2"}}